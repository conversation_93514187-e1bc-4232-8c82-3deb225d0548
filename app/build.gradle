apply plugin: 'com.android.application'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'kotlin-android'
apply plugin: 'dagger.hilt.android.plugin'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.firebase.appdistribution'
apply plugin: 'io.objectbox' // Apply last.
apply plugin: 'org.jetbrains.kotlin.plugin.compose'

static def releaseTime() {
    return new Date().format("yyyyMMddHHmm", TimeZone.getTimeZone("GMT+8:00"))
}

android {
    namespace 'com.one.appointment'
    compileSdk 35

    defaultConfig {
        applicationId "com.one.appointment"
        minSdk 26
        targetSdk 35
        versionCode 23
        versionName "1.3.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true

        setProperty("archivesBaseName", "${applicationId}-v${versionName}(${versionCode})")
    }

    signingConfigs {
        config {
            keyAlias 'One'
            keyPassword 'Appointment'
            storeFile file('../Appointment.jks')
            storePassword 'Appointment'
        }
    }

    buildTypes {
        debug {
            debuggable true
            signingConfig signingConfigs.config
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            debuggable false
            signingConfig signingConfigs.config
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "version"
    productFlavors {
        // Terminal : ./gradlew assembleRelease appDistributionUploadApkRelease
        apk {
            firebaseAppDistribution {
                artifactType = "APK"
                releaseNotes = versionCode
                serviceCredentialsFile = "$rootDir/appointment-5d441-firebase-adminsdk-g6vyb-cbd553c268.json"
                groups = "family"
            }
        }
        // Terminal : ./gradlew bundleRelease appDistributionUploadAabRelease
        aab {
            firebaseAppDistribution {
                artifactType = "AAB"
                releaseNotes = versionCode
                serviceCredentialsFile = "$rootDir/appointment-5d441-firebase-adminsdk-g6vyb-cbd553c268.json"
                groups = "family"
            }
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "Appointment" + "-${variant.name}-v${variant.versionName}-${releaseTime()}.apk"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    // 處理 More than one file was found with OS independent path 'META-INF/DEPENDENCIES'
    packagingOptions {
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'META-INF/DEPENDENCIES'
    }

    viewBinding {
        enabled = true
    }

    buildFeatures {
        buildConfig = true
        compose = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.5.15' // 更新編譯器版本以支援最新 Compose BOM
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.7.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation project(path: ':Core')

    implementation 'androidx.navigation:navigation-fragment-ktx:2.9.3'
    implementation 'androidx.navigation:navigation-ui-ktx:2.9.3'
    // 移除舊版本動畫依賴，使用 BOM 管理的版本

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.cardview:cardview:1.0.0'

    // easyrecyclerview - 暫時註解，需要找替代方案
    // implementation 'com.zhouyou:easyrecyclerview:1.0.5'
    // BaseRecyclerViewAdapterHelper
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.8'


    // live-event-bus - 暫時註解，需要找替代方案
    // implementation 'com.jeremyliao:live-event-bus-x:1.7.2'

    // Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

    // fab
    implementation 'com.github.clans:fab:1.6.4'


    // 舊版 View Calendar 已移除

    // Compose BOM - 更新到最新穩定版本以支援 Calendar Compose
    implementation platform('androidx.compose:compose-bom:2025.07.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.foundation:foundation' // 添加 Foundation 依賴以支援 SnapPositionInLayout
    implementation 'androidx.activity:activity-compose:1.10.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.9.2'
    implementation 'androidx.compose.runtime:runtime-livedata'
    implementation 'androidx.compose.animation:animation' // 添加動畫支援

    // Kizitonwose Calendar Compose - 現代化 Compose 實現（準備中）
    implementation 'com.kizitonwose.calendar:compose:2.7.0'


    //material-dialogs
    implementation 'com.afollestad.material-dialogs:core:3.3.0'
    implementation 'com.afollestad.material-dialogs:input:3.1.1'
    implementation 'com.afollestad.material-dialogs:files:3.1.1'
    implementation 'com.afollestad.material-dialogs:color:3.1.1'
    implementation 'com.afollestad.material-dialogs:datetime:3.1.1'
    implementation 'com.afollestad.material-dialogs:bottomsheets:3.1.1'
    implementation 'com.afollestad.material-dialogs:lifecycle:3.1.1'


    // Add the SDK for Firebase Cloud Messaging
//    implementation 'com.google.firebase:firebase-messaging:23.0.5'
    // Add the SDKs for any other Firebase products you want to use in your app
    // For example, to use Firebase Authentication and Cloud Firestore
//    implementation 'com.google.firebase:firebase-auth:21.0.5'
//    implementation 'com.google.firebase:firebase-firestore:24.1.2'
    // Add the In-App Messaging and Analytics dependencies:
//    implementation 'com.google.firebase:firebase-inappmessaging-display:20.1.2'
    // Add the Firebase SDK for Google Analytics
//    implementation 'com.google.firebase:firebase-analytics:21.0.0'
    // Add the Firebase SDK for Crashlytics
//    implementation 'com.google.firebase:firebase-crashlytics:18.2.11'

    //?
    implementation 'androidx.multidex:multidex:2.0.1'


    // ads
    implementation 'com.google.android.gms:play-services-ads:21.1.0'


    // Google Drive
    implementation 'com.google.android.gms:play-services-auth:20.3.0'
    implementation 'com.google.http-client:google-http-client-gson:1.26.0'
    implementation 'com.google.api-client:google-api-client-android:1.26.0'
    implementation 'com.google.apis:google-api-services-drive:v3-rev136-1.25.0'


    // Java language implementation
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    // Feature module Support
    implementation "androidx.navigation:navigation-dynamic-features-fragment:$nav_version"

    // Testing Navigation
    androidTestImplementation "androidx.navigation:navigation-testing:$nav_version"

    // Jetpack Compose Integration - 移除，專案未使用 Compose
    // implementation "androidx.navigation:navigation-compose:$nav_version"

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    implementation 'androidx.hilt:hilt-navigation-fragment:1.0.0'


    implementation "com.google.dagger:hilt-android:2.42"
    kapt "com.google.dagger:hilt-android-compiler:2.42"


    // required to avoid crash on Android 12 API 31
    implementation 'androidx.work:work-runtime-ktx:2.7.1'

    // 權限處理已改用現代 Android Activity Result API
    // 移除已棄用的 permissions-dispatcher
}
