package com.one.appointment.compose

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.AssistChip
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Badge
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kizitonwose.calendar.compose.HorizontalCalendar
import com.kizitonwose.calendar.compose.rememberCalendarState
import com.kizitonwose.calendar.compose.yearcalendar.rememberYearCalendarState
import com.kizitonwose.calendar.core.CalendarDay
import com.kizitonwose.calendar.core.CalendarMonth
import com.kizitonwose.calendar.core.DayPosition
import com.kizitonwose.calendar.core.ExperimentalCalendarApi
import com.kizitonwose.calendar.core.firstDayOfWeekFromLocale
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.Year
import java.time.YearMonth
import java.time.format.TextStyle
import java.util.Locale

/**
 * 主要的 Compose 日曆螢幕
 */
@Composable
fun CalendarScreen(
    selectedDate: LocalDate = LocalDate.now(),
    appointmentDates: Set<LocalDate> = emptySet(),
    viewMode: CalendarViewMode = CalendarViewMode.MONTH,
    onDateSelected: (LocalDate) -> Unit = {},
    onViewModeChanged: (CalendarViewMode) -> Unit = {},
    onAddAppointment: () -> Unit = {}
) {
    // 使用傳入的參數，不使用內部狀態避免同步問題
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(AppColors.Background)
    ) {

        // 日曆卡片
        CalendarCard(
            selectedDate = selectedDate,
            appointmentDates = appointmentDates,
            viewMode = viewMode,
            onDateSelected = onDateSelected,
            onViewModeChanged = onViewModeChanged
        )
    }
}

/**
 * 日期顯示卡片
 */
@Composable
fun DateDisplayCard(
    selectedDate: LocalDate,
    viewMode: CalendarViewMode,
    onTodayClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = AppColors.Primary
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "${selectedDate.monthValue}月${selectedDate.dayOfMonth}日",
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold,
                    color = AppColors.TextOnPrimary
                )
                Text(
                    text = "${selectedDate.year}年",
                    fontSize = 14.sp,
                    color = AppColors.TextOnPrimary.copy(alpha = 0.9f)
                )
            }

            Button(
                onClick = onTodayClick,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                    contentColor = AppColors.Primary
                ),
                shape = RoundedCornerShape(18.dp)
            ) {
                Text("今天", fontSize = 12.sp)
            }
        }
    }
}

/**
 * 日曆卡片
 */
@Composable
fun CalendarCard(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    viewMode: CalendarViewMode,
    onDateSelected: (LocalDate) -> Unit,
    onViewModeChanged: (CalendarViewMode) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 8.dp, end = 8.dp, top = 8.dp, bottom = 8.dp),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = AppColors.Surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(4.dp)
        ) {
            // 日曆標題欄
            CalendarHeader(
                selectedDate = selectedDate,
                viewMode = viewMode,
                onViewModeChanged = onViewModeChanged,
                onTodayClick = {
                    onDateSelected(LocalDate.now())
                }
            )


            // 日曆內容 - 帶動畫切換
            @OptIn(ExperimentalAnimationApi::class)
            AnimatedContent(
                targetState = viewMode,
                modifier = Modifier.fillMaxWidth(),
                transitionSpec = {
                    slideInHorizontally(
                        initialOffsetX = { fullWidth -> fullWidth },
                        animationSpec = tween(300)
                    ) + fadeIn(animationSpec = tween(300)) togetherWith
                            slideOutHorizontally(
                                targetOffsetX = { fullWidth -> -fullWidth },
                                animationSpec = tween(300)
                            ) + fadeOut(animationSpec = tween(300))
                },
                label = "calendar_view_mode"
            ) { mode ->
                when (mode) {
                    CalendarViewMode.WEEK -> {
                        WeekCalendarView(
                            selectedDate = selectedDate,
                            appointmentDates = appointmentDates,
                            onDateSelected = onDateSelected
                        )
                    }
                    CalendarViewMode.MONTH -> {
//                        MonthCalendar(
//                            selectedDate = selectedDate,
//                            appointmentDates = appointmentDates,
//                            onDateSelected = onDateSelected
//                        )
                        MonthCalendarScreen(
                            selectedDate = selectedDate,
                            appointmentDates = appointmentDates,
                            onDateSelected = onDateSelected,
                        )
                    }
                    CalendarViewMode.YEAR -> {
                        // 年視圖
                        YearCalendarView(
                            selectedDate = selectedDate,
                            appointmentDates = appointmentDates,
                            onDateSelected = onDateSelected,
                            onViewModeChanged = onViewModeChanged
                        )
                    }
                }
            }
        }
    }
}

/**
 * 日曆標題欄
 */
@Composable
fun CalendarHeader(
    selectedDate: LocalDate,
    viewMode: CalendarViewMode,
    onViewModeChanged: (CalendarViewMode) -> Unit,
    onTodayClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                Color(0xFFF5F5F5),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = "${selectedDate.year}",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary,
                modifier = Modifier.clickable {
                    onViewModeChanged(CalendarViewMode.YEAR)
                }
            )
            Text(
                text = "年",
                fontSize = 16.sp,
                color = Color.Gray,
                modifier = Modifier.padding(end = 12.dp)
            )

            Text(
                text = "${selectedDate.monthValue}",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary,
                modifier = Modifier.clickable {
                    onViewModeChanged(CalendarViewMode.MONTH)
                }
            )
            Text(
                text = "月",
                fontSize = 16.sp,
                color = Color.Gray
            )

            Text(
                text = "${selectedDate.dayOfMonth}",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary,
                modifier = Modifier.clickable {
                    onViewModeChanged(CalendarViewMode.WEEK)
                }
            )
            Text(
                text = "日",
                fontSize = 16.sp,
                color = Color.Gray
            )
        }

        // 視圖模式切換按鈕動畫
        val animatedScale by animateFloatAsState(
            targetValue = 1f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            ),
            label = "chip_scale"
        )

        AssistChip(
            onClick = {
                // 循環切換視圖模式：月 → 週 → 年 → 月
                val nextMode = when (viewMode) {
                    CalendarViewMode.MONTH -> CalendarViewMode.WEEK
                    CalendarViewMode.WEEK -> CalendarViewMode.YEAR
                    CalendarViewMode.YEAR -> CalendarViewMode.MONTH
                }
                onViewModeChanged(nextMode)
            },
            modifier = Modifier.scale(animatedScale),
            label = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 視圖模式圖標
                    Text(
                        text = when (viewMode) {
                            CalendarViewMode.MONTH -> "📅"
                            CalendarViewMode.WEEK -> "📊"
                            CalendarViewMode.YEAR -> "🗓️"
                        },
                        fontSize = 12.sp
                    )

                    Text(
                        text = when (viewMode) {
                            CalendarViewMode.MONTH -> "月視圖"
                            CalendarViewMode.WEEK -> "週視圖"
                            CalendarViewMode.YEAR -> "年視圖"
                        },
                        fontSize = 12.sp
                    )
                }
            },
            colors = AssistChipDefaults.assistChipColors(
                containerColor = AppColors.Primary.copy(alpha = 0.1f),
                labelColor = AppColors.Primary
            ),
            border = BorderStroke(
                width = 1.dp,
                color = AppColors.Primary
            )
        )

        Button(
            onClick = onTodayClick,
            colors = ButtonDefaults.buttonColors(
                containerColor = AppColors.Primary,
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Text("今天", fontSize = 12.sp)
        }
    }
}

@OptIn(ExperimentalCalendarApi::class)
@Composable
fun MonthCalendarScreen(
    selectedDate: LocalDate = LocalDate.now(),
    appointmentDates: Set<LocalDate> = emptySet(),
    onDateSelected: (LocalDate) -> Unit = {},
) {
    val currentYear = remember(selectedDate) { Year.of(selectedDate.year) }
    val startYear = remember { currentYear.minusYears(10) }
    val endYear = remember { currentYear.plusYears(10) }
    val firstDayOfWeek = remember { firstDayOfWeekFromLocale() }

    val state = rememberYearCalendarState(
        startYear = startYear,
        endYear = endYear,
        firstVisibleYear = currentYear,
        firstDayOfWeek = firstDayOfWeek,
    )

    // 當選中日期變化時，滾動到對應年份
    LaunchedEffect(selectedDate) {
        val targetYear = Year.of(selectedDate.year)
        state.animateScrollToYear(targetYear)
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = AppColors.Surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp)
        ) {
            // 改用 HorizontalCalendar 來控制顯示的月份數量
            TwoMonthYearView(
                selectedDate = selectedDate,
                appointmentDates = appointmentDates,
                onDateSelected = onDateSelected
            )
        }
    }
}

/**
 * 兩個月份的年視圖
 */
@Composable
fun TwoMonthYearView(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit
) {
    val currentMonth = remember(selectedDate) { YearMonth.from(selectedDate) }
    val startMonth = remember { currentMonth.minusMonths(50) }
    val endMonth = remember { currentMonth.plusMonths(50) }
    val firstDayOfWeek = remember { firstDayOfWeekFromLocale() }

    val state = rememberCalendarState(
        startMonth = startMonth,
        endMonth = endMonth,
        firstVisibleMonth = currentMonth,
        firstDayOfWeek = firstDayOfWeek
    )

    // 當選中日期變化時，滾動到對應月份
    LaunchedEffect(selectedDate) {
        val targetMonth = YearMonth.from(selectedDate)
        state.animateScrollToMonth(targetMonth)
    }

    Column {
        // 年月導航標題
        YearMonthNavigationHeader(
            selectedDate = selectedDate,
            onPreviousMonth = {
                val prevMonth = YearMonth.from(selectedDate).minusMonths(1)
                onDateSelected(prevMonth.atDay(1))
            },
            onNextMonth = {
                val nextMonth = YearMonth.from(selectedDate).plusMonths(1)
                onDateSelected(nextMonth.atDay(1))
            }
        )

        // 使用 HorizontalCalendar 顯示月份
        HorizontalCalendar(
            state = state,
            modifier = Modifier.fillMaxWidth(),
            dayContent = { day ->
                YearViewDayCell(
                    day = day,
                    isSelected = day.date == selectedDate,
                    hasAppointment = appointmentDates.contains(day.date),
                    onClick = { onDateSelected(day.date) }
                )
            },
            monthHeader = { month ->
                CompactMonthHeader(
                    month = month,
                    isCurrentMonth = YearMonth.from(selectedDate) == month.yearMonth,
                    appointmentCount = appointmentDates.count {
                        YearMonth.from(it) == month.yearMonth
                    }
                )
            }
        )
    }
}

/**
 * 年月導航標題
 */
@Composable
fun YearMonthNavigationHeader(
    selectedDate: LocalDate,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 4.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = AppColors.Primary.copy(alpha = 0.1f)
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 上一個月按鈕
            Text(
                text = "◀",
                fontSize = 18.sp,
                color = AppColors.Primary,
                modifier = Modifier
                    .clickable { onPreviousMonth() }
                    .padding(8.dp)
            )

            // 當前年月顯示
            Text(
                text = "${selectedDate.year}年 ${selectedDate.monthValue}月",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary
            )

            // 下一個月按鈕
            Text(
                text = "▶",
                fontSize = 18.sp,
                color = AppColors.Primary,
                modifier = Modifier
                    .clickable { onNextMonth() }
                    .padding(8.dp)
            )
        }
    }
}

/**
 * 緊湊型月份標題
 */
@Composable
fun CompactMonthHeader(
    month: CalendarMonth,
    isCurrentMonth: Boolean = false,
    appointmentCount: Int = 0
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 0.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {

        WeekDaysHeader()
        // 預約數量標記
        if (appointmentCount > 0) {
            @OptIn(ExperimentalMaterial3Api::class)
            Badge(
                containerColor = if (isCurrentMonth)
                    AppColors.Primary
                else
                    AppColors.CalendarEvent,
                contentColor = Color.White
            ) {
                Text(
                    text = appointmentCount.toString(),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        // 當前月份標記
        if (isCurrentMonth) {
            Text(
                text = "●",
                fontSize = 8.sp,
                color = AppColors.Primary
            )
        }
    }
}

/**
 * 年視圖日期單元格
 */
@Composable
fun YearViewDayCell(
    day: CalendarDay,
    isSelected: Boolean,
    hasAppointment: Boolean,
    onClick: () -> Unit
) {
    val isCurrentMonth = day.position == DayPosition.MonthDate
    val isToday = day.date == LocalDate.now()

    // 動畫狀態
    val animatedScale by animateFloatAsState(
        targetValue = if (isSelected) 1.2f else if (hasAppointment && isCurrentMonth) 1.1f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "year_day_scale"
    )

    val animatedElevation by animateDpAsState(
        targetValue = if (isSelected) 4.dp else if (hasAppointment && isCurrentMonth) 2.dp else 0.dp,
        animationSpec = tween(200),
        label = "year_day_elevation"
    )

    Box(
        modifier = Modifier
            .size(36.dp) // 增大尺寸，讓日期不那麼密集
            .scale(animatedScale)
            .clickable(
                enabled = isCurrentMonth,
                onClick = onClick
            ),
        contentAlignment = Alignment.Center
    ) {
        // 選中狀態的背景圓圈
        if (isSelected) {
            Box(
                modifier = Modifier
                    .size(32.dp) // 增大背景圓圈
                    .shadow(
                        elevation = animatedElevation,
                        shape = CircleShape
                    )
                    .background(
                        AppColors.CalendarSelected,
                        CircleShape
                    )
            )
        }

        // 今天狀態的背景圓圈
        if (isToday && !isSelected) {
            Box(
                modifier = Modifier
                    .size(32.dp) // 增大背景圓圈
                    .background(
                        AppColors.CalendarToday,
                        CircleShape
                    )
            )
        }

        // 預約狀態的背景提示
        if (hasAppointment && isCurrentMonth && !isSelected && !isToday) {
            Box(
                modifier = Modifier
                    .size(32.dp) // 增大背景圓圈
                    .background(
                        AppColors.CalendarEvent.copy(alpha = 0.2f),
                        CircleShape
                    )
            )
        }

        // 日期文字
        Text(
            text = day.date.dayOfMonth.toString(),
            fontSize = 14.sp, // 增大字體，讓日期更清楚
            fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
            color = when {
                !isCurrentMonth -> AppColors.TextSecondary.copy(alpha = 0.3f)
                isSelected -> AppColors.TextOnPrimary
                isToday -> AppColors.Primary
                hasAppointment -> AppColors.CalendarEvent
                else -> AppColors.TextPrimary
            }
        )

        // 預約標記 - 改為更明顯的圓點
        if (hasAppointment && isCurrentMonth) {
            Box(
                modifier = Modifier
                    .size(if (isSelected) 6.dp else 5.dp) // 增大預約標記
                    .background(
                        if (isSelected) AppColors.TextOnPrimary else AppColors.CalendarEvent,
                        CircleShape
                    )
                    .align(Alignment.BottomCenter)
                    .offset(y = (-3).dp) // 稍微向上偏移
            )
        }
    }
}

/**
 * 星期標題
 */
@Composable
fun WeekDaysHeader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 0.dp, vertical = 8.dp)
    ) {
        val daysOfWeek = listOf("日", "一", "二", "三", "四", "五", "六")
        daysOfWeek.forEachIndexed { index, day ->
            Text(
                text = day,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = if (index == 0 || index == 6) {
                    AppColors.Primary
                } else {
                    Color.Gray
                }
            )
        }
    }
}

/**
 * 月曆視圖
 */
@Composable
fun MonthCalendar(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit
) {
    val currentMonth = remember(selectedDate) { YearMonth.from(selectedDate) }
    val startMonth = remember { currentMonth.minusMonths(12) }
    val endMonth = remember { currentMonth.plusMonths(12) }
    val firstDayOfWeek = remember { DayOfWeek.SUNDAY }

    val state = rememberCalendarState(
        startMonth = startMonth,
        endMonth = endMonth,
        firstVisibleMonth = currentMonth,
        firstDayOfWeek = firstDayOfWeek
    )

    // 當選中日期變化時，滾動到對應月份
    LaunchedEffect(selectedDate) {
        val targetMonth = YearMonth.from(selectedDate)
        state.animateScrollToMonth(targetMonth)
    }

    HorizontalCalendar(
        state = state,
        dayContent = { day ->
            DayCell(
                day = day,
                isSelected = day.date == selectedDate,
                hasAppointment = appointmentDates.contains(day.date),
                onClick = { onDateSelected(day.date) }
            )
        },
        monthHeader = { month ->
            MonthHeader(month = month)
            WeekDaysHeader()
        },
        modifier = Modifier.padding(horizontal = 8.dp, vertical = 8.dp)
    )
}

/**
 * 週視圖
 */
@Composable
fun WeekCalendarView(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit
) {
    val startOfWeek =
        selectedDate.with(java.time.temporal.TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY))
    val weekDays = (0..6).map { startOfWeek.plusDays(it.toLong()) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(containerColor = AppColors.Surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "${selectedDate.year}年 ${selectedDate.monthValue}月",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary,
                modifier = Modifier
                    .fillMaxWidth() // ← 加這個讓文字能橫向置中
                    .padding(bottom = 16.dp),
                textAlign = TextAlign.Center // ← 這是關鍵
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                weekDays.forEach { date ->
                    WeekDayCell(
                        date = date,
                        isSelected = date == selectedDate,
                        hasAppointment = appointmentDates.contains(date),
                        onClick = { onDateSelected(date) }
                    )
                }
            }
        }
    }
}

/**
 * 週視圖日期單元格
 */
@Composable
fun WeekDayCell(
    date: LocalDate,
    isSelected: Boolean,
    hasAppointment: Boolean,
    onClick: () -> Unit
) {
    val isToday = date == LocalDate.now()
    val dayOfWeek = date.dayOfWeek.getDisplayName(TextStyle.SHORT, Locale.getDefault())

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable { onClick() }
    ) {
        Text(
            text = dayOfWeek,
            fontSize = 12.sp,
            color = AppColors.TextSecondary
        )

        Spacer(modifier = Modifier.height(4.dp))

        Box(
            modifier = Modifier.size(40.dp),
            contentAlignment = Alignment.Center
        ) {
            // 選中狀態的背景圓圈 - 更小更精緻
            if (isSelected) {
                Box(
                    modifier = Modifier
                        .size(32.dp) // 比外框小
                        .background(
                            AppColors.CalendarSelected,
                            CircleShape
                        )
                        .shadow(
                            elevation = if (isSelected) 2.dp else 0.dp,
                            shape = CircleShape
                        )
                )
            }

            // 今天狀態的背景圓圈
            if (isToday && !isSelected) {
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            AppColors.CalendarToday,
                            CircleShape
                        )
                )
            }
            // 內容區域
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = date.dayOfMonth.toString(),
                    fontSize = 16.sp,
                    fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                    color = when {
                        isSelected -> AppColors.TextOnPrimary
                        isToday -> AppColors.Primary
                        else -> AppColors.TextPrimary
                    }
                )

                if (hasAppointment) {
                    Box(
                        modifier = Modifier
                            .size(4.dp)
                            .background(
                                if (isSelected) AppColors.TextOnPrimary else AppColors.CalendarEvent,
                                CircleShape
                            )
                    )
                }
            }
        }
    }
}

/**
 * 年視圖
 */
@Composable
fun YearCalendarView(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onViewModeChanged: (CalendarViewMode) -> Unit
) {
    val year = selectedDate.year
    val months = (1..12).map { YearMonth.of(year, it) }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 年份導航標題
        YearNavigationHeader(
            currentYear = year,
            onPreviousYear = {
                val previousYear = selectedDate.minusYears(1)
                onDateSelected(previousYear)
            },
            onNextYear = {
                val nextYear = selectedDate.plusYears(1)
                onDateSelected(nextYear)
            },
            onYearSelected = { selectedYear ->
                val newDate = LocalDate.of(selectedYear, selectedDate.monthValue, 1)
                onDateSelected(newDate)
            }
        )

        // 月份網格 - 添加年份切換動畫
        @OptIn(ExperimentalAnimationApi::class)
        AnimatedContent(
            targetState = year,
            modifier = Modifier.fillMaxWidth(),
            transitionSpec = {
                slideInHorizontally(
                    initialOffsetX = { fullWidth ->
                        if (targetState > initialState) fullWidth else -fullWidth
                    },
                    animationSpec = tween(300)
                ) + fadeIn(animationSpec = tween(300)) togetherWith
                        slideOutHorizontally(
                            targetOffsetX = { fullWidth ->
                                if (targetState > initialState) -fullWidth else fullWidth
                            },
                            animationSpec = tween(300)
                        ) + fadeOut(animationSpec = tween(300))
            },
            label = "year_change_animation"
        ) { animatedYear ->
            val animatedMonths = (1..12).map { YearMonth.of(animatedYear, it) }

            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(220.dp), // 給定明確的高度約束
                contentPadding = PaddingValues(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(animatedMonths) { month ->
                    YearMonthCard(
                        month = month,
                        selectedDate = selectedDate,
                        appointmentDates = appointmentDates,
                        onDateSelected = onDateSelected,
                        onMonthClick = {
                            onDateSelected(month.atDay(1))
                            onViewModeChanged(CalendarViewMode.MONTH)
                        }
                    )
                }
            }
        }
    }
}

/**
 * 年份導航標題
 */
@Composable
fun YearNavigationHeader(
    currentYear: Int,
    onPreviousYear: () -> Unit,
    onNextYear: () -> Unit,
    onYearSelected: (Int) -> Unit = {}
) {
    var showYearPicker by remember { mutableStateOf(false) }
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 8.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = AppColors.Primary.copy(alpha = 0.1f)
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 上一年按鈕
            Card(
                modifier = Modifier
                    .clickable { onPreviousYear() }
                    .padding(4.dp),
                shape = CircleShape,
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.Primary.copy(alpha = 0.2f)
                ),
            ) {
                Text(
                    text = "◀",
                    fontSize = 20.sp,
                    color = AppColors.Primary,
                    modifier = Modifier.padding(12.dp)
                )
            }

            // 當前年份顯示 - 可點擊快速選擇年份
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "📅",
                    fontSize = 24.sp
                )
                Text(
                    text = "${currentYear}年",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = AppColors.Primary,
                    modifier = Modifier.clickable {
                        showYearPicker = true
                    }
                )
                if (currentYear == LocalDate.now().year) {
                    Text(
                        text = "(今年)",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = AppColors.Primary.copy(alpha = 0.7f)
                    )
                }
            }

            // 下一年按鈕
            Card(
                modifier = Modifier
                    .clickable { onNextYear() }
                    .padding(4.dp),
                shape = CircleShape,
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.Primary.copy(alpha = 0.2f)
                ),
            ) {
                Text(
                    text = "▶",
                    fontSize = 20.sp,
                    color = AppColors.Primary,
                    modifier = Modifier.padding(12.dp)
                )
            }
        }
    }

    // 年份選擇器對話框
    if (showYearPicker) {
        YearPickerDialog(
            currentYear = currentYear,
            onYearSelected = { selectedYear ->
                onYearSelected(selectedYear)
                showYearPicker = false
            },
            onDismiss = {
                showYearPicker = false
            }
        )
    }
}

/**
 * 年份選擇器對話框
 */
@Composable
fun YearPickerDialog(
    currentYear: Int,
    onYearSelected: (Int) -> Unit,
    onDismiss: () -> Unit
) {
    val startYear = 1000
    val endYear = 3000
    val years = (startYear..endYear).toList()

    // 找到當前年份在列表中的位置
    val currentYearIndex = years.indexOf(currentYear).takeIf { it >= 0 } ?: (years.size / 2)
    val listState = rememberLazyListState(initialFirstVisibleItemIndex = maxOf(0, currentYearIndex - 2))

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "選擇年份",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary
            )
        },
        text = {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.Surface
                ),
            ) {
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(years) { year ->
                        YearPickerItem(
                            year = year,
                            isSelected = year == currentYear,
                            isCurrentYear = year == LocalDate.now().year,
                            onClick = { onYearSelected(year) }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = AppColors.Primary
                )
            ) {
                Text("取消")
            }
        },
        dismissButton = {
            TextButton(
                onClick = {
                    onYearSelected(LocalDate.now().year)
                },
                colors = ButtonDefaults.textButtonColors(
                    contentColor = AppColors.Primary
                )
            ) {
                Text("回到今年")
            }
        },
        containerColor = AppColors.Surface,
        shape = RoundedCornerShape(16.dp)
    )
}

/**
 * 年份選擇器項目
 */
@Composable
fun YearPickerItem(
    year: Int,
    isSelected: Boolean,
    isCurrentYear: Boolean,
    onClick: () -> Unit
) {
    val animatedBackgroundColor by animateColorAsState(
        targetValue = when {
            isSelected -> AppColors.Primary.copy(alpha = 0.2f)
            isCurrentYear -> AppColors.CalendarToday.copy(alpha = 0.3f)
            else -> Color.Transparent
        },
        animationSpec = tween(200),
        label = "year_item_background"
    )

    val animatedScale by animateFloatAsState(
        targetValue = if (isSelected) 1.05f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "year_item_scale"
    )

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .scale(animatedScale)
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = animatedBackgroundColor
        ),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 年份顯示
            Text(
                text = "${year}年",
                fontSize = if (isSelected) 18.sp else 16.sp,
                fontWeight = if (isSelected || isCurrentYear) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    isSelected -> AppColors.Primary
                    isCurrentYear -> AppColors.CalendarToday
                    else -> AppColors.TextPrimary
                }
            )

            // 狀態標記
            Row(
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (isCurrentYear) {
                    Text(
                        text = "今年",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = AppColors.CalendarToday,
                        modifier = Modifier
                            .background(
                                AppColors.CalendarToday.copy(alpha = 0.2f),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }

                if (isSelected) {
                    Text(
                        text = "✓",
                        fontSize = 16.sp,
                        color = AppColors.Primary,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

/**
 * 年視圖月份卡片
 */
@Composable
fun YearMonthCard(
    month: YearMonth,
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onMonthClick: () -> Unit
) {
    val isCurrentMonth = YearMonth.from(selectedDate) == month
    val appointmentCount = appointmentDates.count { YearMonth.from(it) == month }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onMonthClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isCurrentMonth)
                AppColors.Primary.copy(alpha = 0.1f)
            else
                AppColors.Surface
        ),
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp, horizontal = 0.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = "${month.monthValue}月",
                    fontSize = 16.sp,
                    fontWeight = if (isCurrentMonth) FontWeight.Bold else FontWeight.Normal,
                    color = if (isCurrentMonth) AppColors.Primary else AppColors.TextPrimary
                )

                Spacer(modifier = Modifier.width(10.dp))

                if (appointmentCount > 0) {
                    @OptIn(ExperimentalMaterial3Api::class)
                    Badge {
                        Text(
                            text = appointmentCount.toString(),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }

    }
}

/**
 * 月份標題
 */
@Composable
fun MonthHeader(month: CalendarMonth) {
    Text(
        text = "${month.yearMonth.year}年 ${month.yearMonth.monthValue}月",
        fontSize = 18.sp,
        fontWeight = FontWeight.Bold,
        color = AppColors.Primary,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp),
        textAlign = TextAlign.Center
    )
}

/**
 * 日期單元格
 */
@Composable
fun DayCell(
    day: CalendarDay,
    isSelected: Boolean,
    hasAppointment: Boolean,
    onClick: () -> Unit,
    isWeekView: Boolean = false
) {
    val isCurrentMonth = day.position == DayPosition.MonthDate
    val isToday = day.date == LocalDate.now()

    // 動畫狀態
    val animatedScale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "scale"
    )

    val animatedElevation by animateDpAsState(
        targetValue = if (isSelected) 8.dp else 0.dp,
        animationSpec = tween(300),
        label = "elevation"
    )

    Box(
        modifier = Modifier
            .size(if (isWeekView) 48.dp else 40.dp)
            .scale(animatedScale)
            .clickable(enabled = isCurrentMonth) { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // 選中狀態的背景圓圈 - 更小更精緻
        if (isSelected) {
            Box(
                modifier = Modifier
                    .size(if (isWeekView) 36.dp else 32.dp) // 比外框小一些
                    .background(
                        AppColors.CalendarSelected,
                        CircleShape
                    )
            )
        }

        // 今天狀態的背景圓圈
        if (isToday && !isSelected) {
            Box(
                modifier = Modifier
                    .size(if (isWeekView) 36.dp else 32.dp)
                    .background(
                        AppColors.CalendarToday,
                        CircleShape
                    )
            )
        }

        // 預約狀態的背景提示
        if (hasAppointment && isCurrentMonth && !isSelected && !isToday) {
            Box(
                modifier = Modifier
                    .size(if (isWeekView) 36.dp else 32.dp)
                    .background(
                        AppColors.CalendarEvent.copy(alpha = 0.1f),
                        CircleShape
                    )
            )
        }
        // 內容區域
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Text(
                text = day.date.dayOfMonth.toString(),
                fontSize = if (isWeekView) 18.sp else 16.sp,
                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                color = when {
                    !isCurrentMonth -> AppColors.TextSecondary.copy(alpha = 0.3f)
                    isSelected -> AppColors.TextOnPrimary
                    isToday -> AppColors.Primary
                    else -> AppColors.TextPrimary
                }
            )

            if (hasAppointment && isCurrentMonth) {
                Spacer(modifier = Modifier.height(2.dp))
                Box(
                    modifier = Modifier
                        .size(if (isSelected) 8.dp else 6.dp)
                        .background(
                            if (isSelected) AppColors.TextOnPrimary else AppColors.CalendarEvent,
                            CircleShape
                        )
                )
            }
        }
    }
}

// 年視圖相關代碼已移除，等待 API 穩定後再實現

/**
 * 日曆視圖模式
 */
enum class CalendarViewMode {
    MONTH, WEEK, YEAR
}

/**
 * 應用程式配色主題
 */
object AppColors {
    // 主要顏色
    val Primary = Color(0xFF3F51B5)           // colorPrimaryBlue
    val PrimaryYellow = Color(0xFFF5A623)     // colorPrimaryYellow
    val Accent = Color(0xFFF5A623)            // colorAccent

    // 文字顏色
    val TextPrimary = Color(0xFF1A1A1A)       // text_primary
    val TextSecondary = Color(0xFF6C757D)     // text_secondary
    val TextOnPrimary = Color.White           // icons

    // 背景顏色
    val Background = Color(0xFFF8F9FA)        // background_light
    val Surface = Color.White                 // card_background
    val SurfaceVariant = Color(0xFFF5F5F5)    // surface_variant
    val White = Color.White

    // 日曆專用顏色
    val CalendarSelected = Color(0xFF3F51B5)  // calendar_selected
    val CalendarToday = Color(0xFFE8EAF6)     // calendar_today
    val CalendarEvent = Color(0xFFF5A623)     // calendar_event
    val CalendarWeekend = Color(0xFFFF5722)   // calendar_weekend

    // 其他顏色
    val Divider = Color(0xFFBDBDBD)           // divider
    val CoolGrey = Color(0xFFAEB0B6)          // cool_grey
    val WarmGrey = Color(0xFF929292)          // warm_grey
}

// ================================
// 預覽功能和數據提供者
// ================================

/**
 * 預覽數據提供者
 */
class CalendarPreviewParameterProvider : PreviewParameterProvider<CalendarPreviewData> {
    override val values = sequenceOf(
        CalendarPreviewData(
            selectedDate = LocalDate.now(),
            appointmentDates = setOf(
                LocalDate.now(),
                LocalDate.now().plusDays(1),
                LocalDate.now().plusDays(3),
                LocalDate.now().plusDays(7),
                LocalDate.now().minusDays(2)
            ),
            viewMode = CalendarViewMode.MONTH
        ),
        CalendarPreviewData(
            selectedDate = LocalDate.now(),
            appointmentDates = setOf(
                LocalDate.now(),
                LocalDate.now().plusDays(2),
                LocalDate.now().plusDays(5)
            ),
            viewMode = CalendarViewMode.WEEK
        ),
        CalendarPreviewData(
            selectedDate = LocalDate.now(),
            appointmentDates = setOf(
                LocalDate.now(),
                LocalDate.now().plusDays(10),
                LocalDate.now().plusDays(20),
                LocalDate.now().plusDays(30)
            ),
            viewMode = CalendarViewMode.YEAR
        )
    )
}

/**
 * 預覽數據類
 */
data class CalendarPreviewData(
    val selectedDate: LocalDate,
    val appointmentDates: Set<LocalDate>,
    val viewMode: CalendarViewMode
)

// ================================
// 預覽組件
// ================================

/**
 * 主要日曆螢幕預覽
 */
@Preview(
    name = "Calendar Screen - Light",
    showBackground = true,
    backgroundColor = 0xFFF8F9FA
)
@Preview(
    name = "Calendar Screen - Dark",
    showBackground = true,
    backgroundColor = 0xFF121212,
    uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES
)
@Composable
fun CalendarScreenPreview(
    @PreviewParameter(CalendarPreviewParameterProvider::class) data: CalendarPreviewData
) {
    MaterialTheme {
        CalendarScreen(
            selectedDate = data.selectedDate,
            appointmentDates = data.appointmentDates,
            viewMode = data.viewMode,
            onDateSelected = { /* 預覽模式 */ },
            onViewModeChanged = { /* 預覽模式 */ },
            onAddAppointment = { /* 預覽模式 */ }
        )
    }
}

/**
 * 日期顯示卡片預覽
 */
@Preview(name = "Date Display Card", showBackground = true)
@Composable
fun DateDisplayCardPreview() {
    MaterialTheme {
        DateDisplayCard(
            selectedDate = LocalDate.now(),
            viewMode = CalendarViewMode.MONTH,
            onTodayClick = { /* 預覽模式 */ }
        )
    }
}

/**
 * 日曆卡片預覽
 */
@Preview(name = "Calendar Card", showBackground = true)
@Composable
fun CalendarCardPreview() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(1),
        LocalDate.now().plusDays(3)
    )

    MaterialTheme {
        CalendarCard(
            selectedDate = LocalDate.now(),
            appointmentDates = testAppointments,
            viewMode = CalendarViewMode.MONTH,
            onDateSelected = { /* 預覽模式 */ },
            onViewModeChanged = { /* 預覽模式 */ }
        )
    }
}

/**
 * 月曆視圖預覽
 */
@Preview(name = "Month Calendar", showBackground = true)
@Composable
fun MonthCalendarPreview() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(2),
        LocalDate.now().plusDays(5),
        LocalDate.now().plusDays(10)
    )

    MaterialTheme {
        MonthCalendar(
            selectedDate = LocalDate.now(),
            appointmentDates = testAppointments,
            onDateSelected = { /* 預覽模式 */ }
        )
    }
}

/**
 * 週曆視圖預覽
 */
@Preview(name = "Week Calendar", showBackground = true)
@Composable
fun WeekCalendarPreview() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(1),
        LocalDate.now().plusDays(3)
    )

    MaterialTheme {
        WeekCalendarView(
            selectedDate = LocalDate.now(),
            appointmentDates = testAppointments,
            onDateSelected = { /* 預覽模式 */ }
        )
    }
}

/**
 * 年曆視圖預覽
 */
@Preview(name = "Year Calendar", showBackground = true)
@Composable
fun YearCalendarPreview() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(30),
        LocalDate.now().plusDays(60),
        LocalDate.now().plusDays(90)
    )

    MaterialTheme {
        YearCalendarView(
            selectedDate = LocalDate.now(),
            appointmentDates = testAppointments,
            onDateSelected = { /* 預覽模式 */ },
            onViewModeChanged = { /* 預覽模式 */ }
        )
    }
}

/**
 * 日期單元格預覽
 */
@Preview(name = "Day Cell - Normal", showBackground = true)
@Composable
fun DayCellNormalPreview() {
    MaterialTheme {
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            DayCell(
                day = CalendarDay(LocalDate.now(), DayPosition.MonthDate),
                isSelected = false,
                hasAppointment = false,
                onClick = { /* 預覽模式 */ }
            )
            DayCell(
                day = CalendarDay(LocalDate.now().plusDays(1), DayPosition.MonthDate),
                isSelected = true,
                hasAppointment = false,
                onClick = { /* 預覽模式 */ }
            )
            DayCell(
                day = CalendarDay(LocalDate.now().plusDays(2), DayPosition.MonthDate),
                isSelected = false,
                hasAppointment = true,
                onClick = { /* 預覽模式 */ }
            )
            DayCell(
                day = CalendarDay(LocalDate.now().plusDays(3), DayPosition.MonthDate),
                isSelected = true,
                hasAppointment = true,
                onClick = { /* 預覽模式 */ }
            )
        }
    }
}

/**
 * 週日期單元格預覽
 */
@Preview(name = "Week Day Cell", showBackground = true)
@Composable
fun WeekDayCellPreview() {
    MaterialTheme {
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            WeekDayCell(
                date = LocalDate.now(),
                isSelected = false,
                hasAppointment = false,
                onClick = { /* 預覽模式 */ }
            )
            WeekDayCell(
                date = LocalDate.now().plusDays(1),
                isSelected = true,
                hasAppointment = false,
                onClick = { /* 預覽模式 */ }
            )
            WeekDayCell(
                date = LocalDate.now().plusDays(2),
                isSelected = false,
                hasAppointment = true,
                onClick = { /* 預覽模式 */ }
            )
        }
    }
}

/**
 * 年月卡片預覽
 */
@Preview(name = "Year Month Card", showBackground = true)
@Composable
fun YearMonthCardPreview() {
    MaterialTheme {
        Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
            YearMonthCard(
                month = YearMonth.now(),
                selectedDate = LocalDate.now(),
                appointmentDates = setOf(LocalDate.now(), LocalDate.now().plusDays(5)),
                onDateSelected = { /* 預覽模式 */ },
                onMonthClick = { /* 預覽模式 */ }
            )
            YearMonthCard(
                month = YearMonth.now().plusMonths(1),
                selectedDate = LocalDate.now(),
                appointmentDates = setOf(),
                onDateSelected = { /* 預覽模式 */ },
                onMonthClick = { /* 預覽模式 */ }
            )
            YearMonthCard(
                month = YearMonth.now().plusMonths(2),
                selectedDate = LocalDate.now(),
                appointmentDates = setOf(
                    LocalDate.now().plusMonths(2).withDayOfMonth(10),
                    LocalDate.now().plusMonths(2).withDayOfMonth(15),
                    LocalDate.now().plusMonths(2).withDayOfMonth(20)
                ),
                onDateSelected = { /* 預覽模式 */ },
                onMonthClick = { /* 預覽模式 */ }
            )
        }
    }
}

/**
 * 功能測試內容
 */
@Composable
fun FunctionTestCalendarContent(
    selectedDate: LocalDate,
    appointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onViewModeChanged: (CalendarViewMode) -> Unit,
    onAddAppointment: () -> Unit
) {
    CalendarScreen(
        selectedDate = selectedDate,
        appointmentDates = appointmentDates,
        viewMode = CalendarViewMode.MONTH,
        onDateSelected = onDateSelected,
        onViewModeChanged = onViewModeChanged,
        onAddAppointment = onAddAppointment
    )
}

/**
 * 簡單測試內容
 */
@Composable
fun TestCalendarContent() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(2),
        LocalDate.now().plusDays(5)
    )

    CalendarScreen(
        selectedDate = LocalDate.now(),
        appointmentDates = testAppointments,
        viewMode = CalendarViewMode.MONTH,
        onDateSelected = { /* 測試模式 */ },
        onViewModeChanged = { /* 測試模式 */ },
        onAddAppointment = { /* 測試模式 */ }
    )
}

/**
 * 功能測試內容預覽
 */
@Preview(name = "Function Test Content", showBackground = true)
@Composable
fun FunctionTestContentPreview() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(1),
        LocalDate.now().plusDays(3),
        LocalDate.now().plusDays(7)
    )

    MaterialTheme {
        FunctionTestCalendarContent(
            selectedDate = LocalDate.now(),
            appointmentDates = testAppointments,
            onDateSelected = { /* 預覽模式 */ },
            onViewModeChanged = { /* 預覽模式 */ },
            onAddAppointment = { /* 預覽模式 */ }
        )
    }
}

/**
 * 簡單測試內容預覽
 */
@Preview(name = "Test Content", showBackground = true)
@Composable
fun TestContentPreview() {
    MaterialTheme {
        TestCalendarContent()
    }
}

/**
 * 水平年曆視圖預覽
 */
@Preview(name = "Horizontal Year Calendar", showBackground = true)
@Composable
fun HorizontalYearCalendarScreenPreview() {
    val testAppointments = setOf(
        LocalDate.now(),
        LocalDate.now().plusDays(30),
        LocalDate.now().plusDays(60),
        LocalDate.now().plusDays(90),
        LocalDate.now().plusDays(120),
        LocalDate.now().plusDays(150),
        LocalDate.now().minusDays(30),
        LocalDate.now().minusDays(60)
    )

    MaterialTheme {
        MonthCalendarScreen(
            selectedDate = LocalDate.now(),
            appointmentDates = testAppointments,
            onDateSelected = { /* 預覽模式 */ }
        )
    }
}

/**
 * 增強版年份標題預覽
 */
@Preview(name = "Enhanced Year Header", showBackground = true)
@Composable
fun EnhancedYearHeaderPreview() {
    MaterialTheme {
        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            // 使用假的 CalendarYear 對象進行預覽
            val currentYear = Year.now()
            val nextYear = currentYear.plusYears(1)

            // 創建簡單的預覽版本
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.Primary.copy(alpha = 0.1f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "📅",
                        fontSize = 20.sp,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text(
                        text = "${currentYear.value}年",
                        fontSize = 26.sp,
                        fontWeight = FontWeight.ExtraBold,
                        color = AppColors.Primary
                    )
                    Text(
                        text = " (今年)",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = AppColors.Primary.copy(alpha = 0.7f)
                    )
                }
            }

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.SurfaceVariant
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${nextYear.value}年",
                        fontSize = 22.sp,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.TextPrimary
                    )
                }
            }
        }
    }
}

/**
 * 增強版月份標題預覽
 */
@Preview(name = "Enhanced Month Header", showBackground = true)
@Composable
fun EnhancedMonthHeaderPreview() {
    MaterialTheme {
        Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
            // 創建簡單的預覽版本
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 4.dp),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.Primary.copy(alpha = 0.15f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${YearMonth.now().monthValue}月",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.Primary
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    @OptIn(ExperimentalMaterial3Api::class)
                    Badge(
                        containerColor = AppColors.Primary,
                        contentColor = Color.White
                    ) {
                        Text(
                            text = "3",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "●",
                        fontSize = 8.sp,
                        color = AppColors.Primary
                    )
                }
            }

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 4.dp),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = AppColors.CalendarEvent.copy(alpha = 0.1f)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${YearMonth.now().plusMonths(1).monthValue}月",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = AppColors.CalendarEvent
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    @OptIn(ExperimentalMaterial3Api::class)
                    Badge(
                        containerColor = AppColors.CalendarEvent,
                        contentColor = Color.White
                    ) {
                        Text(
                            text = "1",
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 4.dp),
                shape = RoundedCornerShape(8.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Transparent
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${YearMonth.now().plusMonths(2).monthValue}月",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = AppColors.TextPrimary
                    )
                }
            }
        }
    }
}

/**
 * 年視圖日期單元格預覽
 */
@Preview(name = "Year View Day Cell", showBackground = true)
@Composable
fun YearViewDayCellPreview() {
    MaterialTheme {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.padding(16.dp)
        ) {
            YearViewDayCell(
                day = CalendarDay(LocalDate.now(), DayPosition.MonthDate),
                isSelected = false,
                hasAppointment = false,
                onClick = { /* 預覽模式 */ }
            )
            YearViewDayCell(
                day = CalendarDay(LocalDate.now().plusDays(1), DayPosition.MonthDate),
                isSelected = true,
                hasAppointment = false,
                onClick = { /* 預覽模式 */ }
            )
            YearViewDayCell(
                day = CalendarDay(LocalDate.now().plusDays(2), DayPosition.MonthDate),
                isSelected = false,
                hasAppointment = true,
                onClick = { /* 預覽模式 */ }
            )
            YearViewDayCell(
                day = CalendarDay(LocalDate.now().plusDays(3), DayPosition.MonthDate),
                isSelected = true,
                hasAppointment = true,
                onClick = { /* 預覽模式 */ }
            )
        }
    }
}

/**
 * 年份選擇器對話框預覽
 */
@Preview(name = "Year Picker Dialog", showBackground = true)
@Composable
fun YearPickerDialogPreview() {
    MaterialTheme {
        YearPickerDialog(
            currentYear = LocalDate.now().year,
            onYearSelected = { /* 預覽模式 */ },
            onDismiss = { /* 預覽模式 */ }
        )
    }
}

/**
 * 年份導航標題預覽
 */
@Preview(name = "Year Navigation Header", showBackground = true)
@Composable
fun YearNavigationHeaderPreview() {
    MaterialTheme {
        YearNavigationHeader(
            currentYear = LocalDate.now().year,
            onPreviousYear = { /* 預覽模式 */ },
            onNextYear = { /* 預覽模式 */ },
            onYearSelected = { /* 預覽模式 */ }
        )
    }
}

/**
 * 視圖模式選擇器
 */
@Composable
fun ViewModeSelector(
    currentMode: CalendarViewMode,
    onModeSelected: (CalendarViewMode) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "選擇視圖模式",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = AppColors.Primary
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CalendarViewMode.values().forEach { mode ->
                    ViewModeOption(
                        mode = mode,
                        isSelected = mode == currentMode,
                        onClick = { onModeSelected(mode) }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    text = "取消",
                    color = AppColors.Primary
                )
            }
        },
        containerColor = AppColors.Surface,
        titleContentColor = AppColors.Primary,
        textContentColor = AppColors.TextPrimary
    )
}

/**
 * 視圖模式選項
 */
@Composable
fun ViewModeOption(
    mode: CalendarViewMode,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                AppColors.Primary.copy(alpha = 0.1f)
            else
                Color.Transparent
        ),
        border = if (isSelected)
            BorderStroke(2.dp, AppColors.Primary)
        else
            BorderStroke(1.dp, AppColors.TextSecondary.copy(alpha = 0.3f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 模式圖標
            Text(
                text = when (mode) {
                    CalendarViewMode.MONTH -> "📅"
                    CalendarViewMode.WEEK -> "📊"
                    CalendarViewMode.YEAR -> "🗓️"
                },
                fontSize = 24.sp
            )

            Column {
                Text(
                    text = when (mode) {
                        CalendarViewMode.MONTH -> "月視圖"
                        CalendarViewMode.WEEK -> "週視圖"
                        CalendarViewMode.YEAR -> "年視圖"
                    },
                    fontSize = 16.sp,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    color = if (isSelected) AppColors.Primary else AppColors.TextPrimary
                )

                Text(
                    text = when (mode) {
                        CalendarViewMode.MONTH -> "顯示完整月份日曆"
                        CalendarViewMode.WEEK -> "顯示當前週的日期"
                        CalendarViewMode.YEAR -> "顯示全年月份概覽"
                    },
                    fontSize = 12.sp,
                    color = AppColors.TextSecondary
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "已選中",
                    tint = AppColors.Primary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 視圖模式選擇器預覽
 */
@Preview(name = "View Mode Selector", showBackground = true)
@Composable
fun ViewModeSelectorPreview() {
    MaterialTheme {
        ViewModeSelector(
            currentMode = CalendarViewMode.MONTH,
            onModeSelected = { /* 預覽模式 */ },
            onDismiss = { /* 預覽模式 */ }
        )
    }
}

/**
 * 視圖模式選項預覽
 */
@Preview(name = "View Mode Option", showBackground = true)
@Composable
fun ViewModeOptionPreview() {
    MaterialTheme {
        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
            ViewModeOption(
                mode = CalendarViewMode.MONTH,
                isSelected = true,
                onClick = { /* 預覽模式 */ }
            )
            ViewModeOption(
                mode = CalendarViewMode.WEEK,
                isSelected = false,
                onClick = { /* 預覽模式 */ }
            )
            ViewModeOption(
                mode = CalendarViewMode.YEAR,
                isSelected = false,
                onClick = { /* 預覽模式 */ }
            )
        }
    }
}
