package com.one.appointment.compose

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.one.appointment.entity.Appointment
import java.time.LocalDate

/**
 * Java 友好的接口
 */
interface DateSelectedListener {
    fun onDateSelected(date: LocalDate)
}

interface ViewModeChangedListener {
    fun onViewModeChanged(mode: CalendarViewMode)
}

interface AddAppointmentListener {
    fun onAddAppointment()
}

interface AppointmentDeleteListener {
    fun onAppointmentDelete(appointment: Appointment)
}

interface AppointmentClickListener {
    fun onAppointmentClick(appointment: Appointment)
}

/**
 * Kotlin 橋接類，用於在 Java Fragment 中設置 Compose Calendar
 */
object CalendarBridge {
    /**
     * 設置功能測試內容
     */
    fun setupFunctionTestContent(
        composeView: ComposeView,
        selectedDate: LocalDate,
        appointmentDates: Set<LocalDate>,
        dateListener: DateSelectedListener,
        viewModeListener: ViewModeChangedListener,
        addAppointmentListener: AddAppointmentListener
    ) {
        composeView.setContent {
            FunctionTestCalendarContent(
                selectedDate = selectedDate,
                appointmentDates = appointmentDates,
                onDateSelected = { date -> dateListener.onDateSelected(date) },
                onViewModeChanged = { mode -> viewModeListener.onViewModeChanged(mode) },
                onAddAppointment = { addAppointmentListener.onAddAppointment() }
            )
        }
    }

    /**
     * 設置完整的 Compose Calendar（生產版本）
     */
    fun setupProductionCalendarContent(
        composeView: ComposeView,
        selectedDate: LocalDate,
        appointmentDates: Set<LocalDate>,
        appointments: List<Appointment>,
        dateListener: DateSelectedListener,
        viewModeListener: ViewModeChangedListener,
        addAppointmentListener: AddAppointmentListener,
        deleteListener: AppointmentDeleteListener,
        clickListener: AppointmentClickListener
    ) {
        composeView.setContent {
            ResponsiveCalendarScreen(
                initialSelectedDate = selectedDate,
                appointments = appointments,
                initialAppointmentDates = appointmentDates,
                onDateSelected = { date -> dateListener.onDateSelected(date) },
                onViewModeChanged = { mode -> viewModeListener.onViewModeChanged(mode) },
                onAddAppointment = { addAppointmentListener.onAddAppointment() },
                onAppointmentDelete = { appointment -> deleteListener.onAppointmentDelete(appointment) },
                onAppointmentClick = { appointment -> clickListener.onAppointmentClick(appointment) }
            )
        }
    }
}

/**
 * 響應式日曆螢幕 - 使用內部狀態管理
 */
@Composable
fun ResponsiveCalendarScreen(
    initialSelectedDate: LocalDate,
    appointments: List<Appointment>,
    initialAppointmentDates: Set<LocalDate>,
    onDateSelected: (LocalDate) -> Unit,
    onViewModeChanged: (CalendarViewMode) -> Unit,
    onAddAppointment: () -> Unit,
    onAppointmentDelete: (Appointment) -> Unit = {},
    onAppointmentClick: (Appointment) -> Unit = {}
) {
    // 使用 Compose 狀態管理
    var selectedDate by remember { mutableStateOf(initialSelectedDate) }
    var appointmentDates by remember { mutableStateOf(initialAppointmentDates) }
    var viewMode by remember { mutableStateOf(CalendarViewMode.MONTH) }

    // 當外部數據變化時更新內部狀態
    LaunchedEffect(initialSelectedDate) {
        selectedDate = initialSelectedDate
    }

    LaunchedEffect(initialAppointmentDates) {
        appointmentDates = initialAppointmentDates
    }

    AppointmentListScreen(
        selectedDate = selectedDate,
        appointments = appointments,
        appointmentDates = appointmentDates,
        viewMode = viewMode,
        onDateSelected = { date ->
            selectedDate = date
            onDateSelected(date)
        },
        onViewModeChanged = { mode ->
            viewMode = mode
            onViewModeChanged(mode)
        },
        onAddAppointment = onAddAppointment,
        onAppointmentDelete = onAppointmentDelete,
        onAppointmentClick = onAppointmentClick
    )
}
